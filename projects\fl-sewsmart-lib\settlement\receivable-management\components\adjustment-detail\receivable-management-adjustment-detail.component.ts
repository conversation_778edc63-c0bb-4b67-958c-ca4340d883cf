import { Component, Input, OnInit, <PERSON>Changes, OnD<PERSON>roy,  } from '@angular/core';
import { FormArray, FormBuilder, FormGroup,AbstractControl } from '@angular/forms';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FlcUtilService } from 'fl-common-lib';
import { PageEditModeEnum, ReceivableDetail, V1BillsReceivableLineAdjustment } from '../../models/receivable-detail.interface';
import { ReceivableService } from '../../receivable-service';
import { ReceivableSubjectEventType } from '../../models/receivable-detail.enum';

@Component({
  selector: 'flss-receivable-management-adjustment-detail',
  templateUrl: './receivable-management-adjustment-detail.component.html',
  styleUrls: ['./receivable-management-adjustment-detail.component.scss'],
})
export class ReceivableManagementAdjustmentDetailComponent implements OnInit, OnDestroy {
  @Input() detailInfo?: ReceivableDetail;
  @Input() editMode: PageEditModeEnum = PageEditModeEnum.add;

  pageEditModeEnum = PageEditModeEnum;
  
  // 调整项目相关
  availableAdjustments: any[] = [];
  selectedAdjustments: any[] = [];
  selectedAdjustmentIds: string[] = [];
  adjustmentModalVisible = false;
  adjustmentSearchText: string = '';

  subscribeKey = 'receivable-adjustment-detail';

  // 统计数据
  adjustmentStatistic: { [key: string]: number } = {
    total_included: 0,
    total_excluded: 0,
  };

  disabledBtn = true;
  customer: { customer_id?: string; customer_name?: string } = {};

  // 调整项弹窗数据
  adjustVisible = false;
  adjustmentList: any[] = []; // 调整项
  checkedAdjustment: any[] = []; //当前选中项
  checkedAdjustmentIds: number[] = []; //当前选中项ids

  // 事件汇报弹窗数据
  eventVisible = false;
  eventOrderCode: string = '';
  eventOrderProductionType: string = '';
  eventOrderUUKey: string = '';
  eventList: any[] = []; // 事件汇报项
  checkedEventIds: number[] = []; //当前选中项
  event_form: FormGroup = new FormGroup({ event_form_list: new FormArray([]) });

  // 统计数据
  processStatisticData: { [key: string]: number } = {
    total: 0, //总计
  };

  get event_form_array(): FormArray {
    return this.event_form?.get('event_form_list') as FormArray;
  }


    ReceivableSettlementForm?: FormGroup;
  get outbound_order_list(): FormArray {
    return this.ReceivableSettlementForm?.get('outbound_order_list') as FormArray;
  }
  constructor(
    private _fb: FormBuilder,
    private modal: NzModalService,
    private _msg: NzMessageService,
    private _service: ReceivableService,
    private _flcUtil: FlcUtilService
  ) {}

  ngOnInit() {
    this._service.addSubjectListener(
      this.subscribeKey,
      [ReceivableSubjectEventType.addOrder, ReceivableSubjectEventType.delOrder, ReceivableSubjectEventType.adjustmentPriceChange],
      (res) => {


        if (res.type === ReceivableSubjectEventType.addOrder) {

          console.log(res)
          this.disabledBtn = !res.data.customer_code;
          this.customer = {
            customer_id: res.data.customer_code,
            customer_name: res.data.customer_name
          };
          this.ReceivableSettlementForm = res.data.form;

          if (this.outbound_order_list.length) this.disabledBtn = false;

          if (res.data?.type !== 'detail') {
            // 新增订单时的处理
            if (this.checkedAdjustmentIds.length) this.initLines();
          } else {
            // 详情数据的处理
            this.initializeAdjustmentFromDetail();
          }

          this.initAllCheck();
        } else if (res.type === ReceivableSubjectEventType.delOrder) {
          this.initAllCheck();
        }
      }
    )
  }

  ngOnChanges(changes: SimpleChanges): void {
    // 不再在 ngOnChanges 中处理详情数据，改为通过 addOrder 事件处理
  }

  ngOnDestroy(): void {
    this._service.removeSubjectListener(this.subscribeKey);
  }

  /**
   * 从详情数据初始化调整项
   */
  private initializeAdjustmentFromDetail(): void {
    if (!this.outbound_order_list || this.outbound_order_list.length === 0) {
      return;
    }

    // 从表单数据中收集所有调整项，去重
    const adjustmentMap = new Map();

    this.outbound_order_list.controls.forEach((outboundControl: any) => {
      const linesArray = outboundControl.get('lines') as FormArray;
      linesArray?.controls.forEach((lineControl: any) => {
        const adjustmentLines = lineControl.get('adjustment_lines') as FormArray;
        if (adjustmentLines && adjustmentLines.length > 0) {
          adjustmentLines.controls.forEach((adjControl: any) => {
            const adjValue = adjControl.value;
            if (adjValue.adjustment_item_id && !adjustmentMap.has(adjValue.adjustment_item_id)) {
              adjustmentMap.set(adjValue.adjustment_item_id, {
                adjustment_item_id: adjValue.adjustment_item_id,
                adjustment_item_code: adjValue.adjustment_item_code,
                adjustment_item_name: adjValue.adjustment_item_name,
                adjustment_item_type: adjValue.adjustment_item_type,
              });
            }
          });
        }
      });
    });

    // 设置调整项配置
    this.checkedAdjustment = Array.from(adjustmentMap.values());
    this.checkedAdjustmentIds = this.checkedAdjustment.map((item: any) => item.adjustment_item_id);

    // 计算详情总计
    this.outbound_order_list?.controls.forEach((item: any) => {
      const lines = item.get('lines') as FormArray;
      lines?.controls.forEach((line: any) => {
        this.calcRowPrice(line);
      });
    });
    this.calcTotalPrice();
  }


  /* 获取调整设置中的数据并显示调整项选择的气泡弹窗 */
  showAdjust(): void {
    this.adjustVisible = false;
    this._service.getSettlementAdjust().subscribe((res: any) => {
      if (res.code === 200) {
        this.adjustmentList = res?.data?.datalist?.map((item: any) => {
          return {
            adjustment_item_code: item.code,
            adjustment_item_name: item.name,
            adjustment_item_id: item.id,
            adjustment_item_type: item.adjust_type,
            checked: this.checkedAdjustmentIds.includes(item.id),
          };
        });
        this.adjustmentList?.length ? (this.adjustVisible = true) : this._msg.error('暂无调整项可选，请先去配置');
      }
    });
  }

  adjustCancel(): void {
    this.adjustVisible = false;
  }

  adjustOk(): void {
    const _currentCheckedIds = this.adjustmentList.filter((item) => item.checked).map((item) => item.adjustment_item_id);
    const _addAdjustmentIds = _currentCheckedIds.filter((item) => !this.checkedAdjustmentIds.includes(item));
    const _currentNotCheckedIds = this.adjustmentList.filter((item) => !item.checked).map((item) => item.adjustment_item_id);
    const _deleteAdjustment = this.checkedAdjustment.filter((item) => _currentNotCheckedIds.includes(item.adjustment_item_id));

    // 删除行
    _deleteAdjustment.forEach((item) => {
      const _index = this.checkedAdjustmentIds.findIndex((id) => item.adjustment_item_id === id);
      this.checkedAdjustmentIds.splice(_index, 1);
      this.checkedAdjustment.splice(_index, 1);
      this.onRemove(item);
    });

    // 添加新的调整项
    _addAdjustmentIds.forEach((id) => {
      const _item = this.adjustmentList.find((aItem) => aItem.adjustment_item_id === id);
      _item && this.checkedAdjustmentIds.push(_item.adjustment_item_id);
      _item && this.checkedAdjustment.push(_item);
    });

    // 重新构建所有行的adjustment_lines，确保顺序与checkedAdjustment一致
    if (_addAdjustmentIds.length || _deleteAdjustment.length) {
      this.rebuildAdjustmentLines();
    }
    this.adjustVisible = false;
  }

  onRemove(item: any, isRemove = false): void {
    if (isRemove) {
      const _index = this.checkedAdjustmentIds.findIndex((id) => id === item.adjustment_item_id);
      this.checkedAdjustment.splice(_index, 1);
      this.checkedAdjustmentIds.splice(_index, 1);

      // 重新构建adjustment_lines以确保顺序一致
      this.rebuildAdjustmentLines();
    } else {
      // 这是从adjustOk方法调用的，只需要移除对应的FormControl
      this.outbound_order_list?.controls.forEach((control: any) => {
        const _linesControl = control.get('lines') as FormArray;
        _linesControl?.controls.forEach((_lineControl: any) => {
          const ajustmentControls = _lineControl.get('adjustment_lines') as FormArray;
          const _controlIndex = ajustmentControls?.controls.findIndex(
            (control: any) => control.get('adjustment_item_id')?.value === item.adjustment_item_id
          );
          if (_controlIndex >= 0) {
            ajustmentControls?.removeAt(_controlIndex);
          }
          // 总计
          this.calcRowPrice(_lineControl);
        });
      });
    }
    this._service.sendSubjectEvent(ReceivableSubjectEventType.adjustmentPriceChange);
  }

  initLines(): void {
    this.outbound_order_list?.controls.forEach((control: any) => {
      (control.get('lines') as FormArray)?.controls?.forEach((lineControl: any) => {
        let _formArray = lineControl.get('adjustment_lines') as FormArray;

        // 如果adjustment_lines不存在，则创建一个新的FormArray
        if (!_formArray) {
          _formArray = this._fb.array([]);
          lineControl.addControl('adjustment_lines', _formArray);
        }

        const _values = _formArray.getRawValue() || [];
        this.checkedAdjustment.forEach((aItem: any) => {
          // 不存在的出库单 则添加
          if (_values.findIndex((_value: any) => _value.adjustment_item_id === aItem.adjustment_item_id) === -1) {
            const _aGroup = this._fb.group({
              // 调整项ID：新建时为0
              id: ['0'],

              // 应收明细ID：新建时为0，编辑时会在提交时设置
              bills_receivable_outbound_rel_id: ['0'],

              // 调整项基础信息
              adjustment_item_id: [aItem.adjustment_item_id],
              adjustment_item_type: [aItem.adjustment_item_type],
              adjustment_item_code: [aItem.adjustment_item_code],
              adjustment_item_name: [aItem.adjustment_item_name],

              // 调整价格
              adjustment_price: [null],

              // 是否计入
              count_adjustment: [false],
            });
            _formArray.push(_aGroup);
          }
        });
      });
    });
  }

  /**
   * 重新构建所有行的adjustment_lines，确保顺序与checkedAdjustment一致
   */
  rebuildAdjustmentLines(): void {
    this.outbound_order_list?.controls.forEach((control: any) => {
      (control.get('lines') as FormArray)?.controls?.forEach((lineControl: any) => {
        let _formArray = lineControl.get('adjustment_lines') as FormArray;

        // 如果adjustment_lines不存在，则创建一个新的FormArray
        if (!_formArray) {
          _formArray = this._fb.array([]);
          lineControl.addControl('adjustment_lines', _formArray);
        }

        const _currentValues = _formArray.getRawValue() || [];

        // 清空现有的FormArray
        while (_formArray.length !== 0) {
          _formArray.removeAt(0);
        }

        // 按照checkedAdjustment的顺序重新添加FormGroup
        this.checkedAdjustment.forEach((aItem: any) => {
          // 查找是否有现有的数据
          const existingData = _currentValues.find((value: any) => value.adjustment_item_id === aItem.adjustment_item_id);

          const _aGroup = this._fb.group({
            // 调整项ID：保持现有ID或新建为0
            id: [existingData?.id || '0'],

            // 应收明细ID：保持现有值或新建为0
            bills_receivable_outbound_rel_id: [existingData?.bills_receivable_outbound_rel_id || '0'],

            // 调整项基础信息
            adjustment_item_id: [aItem.adjustment_item_id],
            adjustment_item_type: [aItem.adjustment_item_type],
            adjustment_item_code: [aItem.adjustment_item_code],
            adjustment_item_name: [aItem.adjustment_item_name],

            // 调整价格
            adjustment_price: [existingData?.adjustment_price || null],

            // 调整备注
            adjustment_remark: [existingData?.adjustment_remark || ''],

            // 是否计入
            count_adjustment: [existingData?.count_adjustment || false],
          });
          _formArray.push(_aGroup);
        });
      });
    });

    // 重新计算价格和统计
    this.outbound_order_list?.controls.forEach((item: any) => {
      const lines = item.get('lines') as FormArray;
      lines?.controls.forEach((line: any) => {
        this.calcRowPrice(line);
      });
    });
    this.calcTotalPrice();
    this.initAllCheck();

    // 通知loan-detail组件重新计算调整金额
    this._service.sendSubjectEvent(ReceivableSubjectEventType.adjustmentPriceChange);
  }

  //价格变动
  onChangePrice(control: AbstractControl) {
    this.calcRowPrice(control);
    this.calcTotalPrice();
    this._service.sendSubjectEvent(ReceivableSubjectEventType.adjustmentPriceChange, control);
  }

  // 计算调整金额
  calcRowPrice(control: AbstractControl) {
    const ajustmentControls = control.get('adjustment_lines') as FormArray;
    if (!ajustmentControls) {
      control.get('total_adjust')?.setValue(0);
      return;
    }

    const _values = ajustmentControls.getRawValue() || [];
    let _totalPrice = 0;
    _values.forEach((item: any) => {
      if (item.adjustment_item_type === 1) {
        _totalPrice = this._flcUtil.accSubPlus(_totalPrice, item.adjustment_price ?? 0);
      } else {
        _totalPrice = this._flcUtil.accAddPlus(item.adjustment_price ?? 0, _totalPrice);
      }
    });
    control.get('total_adjust')?.setValue(this._flcUtil.toFixed(_totalPrice, 2));
  }

  /* 计算总价 */
  calcTotalPrice() {
    const _outbound_order_list = this.outbound_order_list.getRawValue();
    const _totalValue: any = {};
    let _totalPrice = 0;
    _outbound_order_list?.forEach((outboundItem: any) => {
      outboundItem.lines?.forEach((line: any) => {
        line.adjustment_lines?.forEach((item: any) => {
          if (item.adjustment_item_type === 1) {
            _totalPrice = this._flcUtil.accSubPlus(_totalPrice, item.adjustment_price ?? 0);
            _totalValue[item.adjustment_item_code] = this._flcUtil.accSubPlus(
              _totalValue[item.adjustment_item_code] ?? 0,
              item.adjustment_price ?? 0
            );
          } else {
            _totalPrice = this._flcUtil.accAddPlus(item.adjustment_price ?? 0, _totalPrice ?? 0);
            _totalValue[item.adjustment_item_code] = this._flcUtil.accAddPlus(
              item.adjustment_price ?? 0,
              _totalValue[item.adjustment_item_code] ?? 0
            );
          }
        });
      });
    });
    this.processStatisticData = _totalValue;
    this.processStatisticData.total = _totalPrice;
  }

  /* 计入复选框事件 */
  onChangeCountAdjustment(index: number, lineControl: AbstractControl) {
    this.changeAllCountAjustMent(index);
    this.calcTotalPrice();
    this._service.sendSubjectEvent(ReceivableSubjectEventType.adjustmentPriceChange, lineControl);
  }

  private changeAllCountAjustMent(index: number) {
    const checkArray: boolean[] = [];
    this.outbound_order_list.controls.forEach((control: any) => {
      const lines = control.get('lines') as FormArray;
      lines.controls.forEach((_lineControl: any) => {
        const adjustmentLines = _lineControl.get('adjustment_lines') as FormArray;
        const _checked = adjustmentLines?.controls[index]?.get('count_adjustment')?.value;
        checkArray.push(_checked || false);
      });
    });
    if (!this.checkedAdjustment[index]) return;
    this.checkedAdjustment[index].count_adjustment = checkArray.every((item) => item);
    this.checkedAdjustment[index].indeterminate = !this.checkedAdjustment[index].count_adjustment && checkArray.some((item) => item);
  }

  private initAllCheck() {
    this.outbound_order_list.controls.forEach((control: any) => {
      const lines = control.get('lines') as FormArray;
      lines.controls.forEach((_lineControl: any) => {
        const adjustmentLines = _lineControl.get('adjustment_lines') as FormArray;
        if (adjustmentLines) {
          adjustmentLines.controls.forEach((_: any, index: number) => {
            this.changeAllCountAjustMent(index);
          });
        }
      });
    });
  }

  /* 表头计入和不计入事件 */
  onChangeAllCountAdjustment(e: boolean, index: number): void {
    this.outbound_order_list.controls.forEach((_group: any) => {
      const _lineControls = _group.get('lines') as FormArray;
      _lineControls?.controls.forEach((_lineControl: any) => {
        const adjustmentLines = _lineControl.get('adjustment_lines') as FormArray;
        adjustmentLines?.controls[index]?.get('count_adjustment')?.setValue(e);
      });
    });
    this.calcTotalPrice();
    this._service.sendSubjectEvent(ReceivableSubjectEventType.adjustmentPriceChange);
  }

  // 事件汇报
  showEventList(lineControl: AbstractControl, outboundControl: AbstractControl) {
    this.eventOrderCode = lineControl.get('order_code')?.value;
    this.eventOrderUUKey =
      lineControl.get('order_uuid')?.value +
      '-' +
      lineControl.get('style_code')?.value +
      '-' +
      lineControl.get('po_code')?.value +
      '-' +
      lineControl.get('color_id')?.value;

    // 这里需要根据实际的API调用来获取事件列表
    // 暂时使用空数组，实际应该调用相应的API
    this.eventList = [];
    this.event_form_array.clear();
    this.eventVisible = true;
  }

  eventOk(): void {
    // 事件确认逻辑
    this.eventVisible = false;
  }

  eventCancel(): void {
    this.eventVisible = false;
  }

  addEventForm(event_line: any) {
    const group = this._fb.group({
      event_id: [event_line?.event_id ?? 0],
      adjust_code: [event_line?.adjust_code],
      adjust_id: [event_line?.adjust_id ?? 0],
      adjust_type: [event_line?.adjust_type],
      adjust_type_value: [event_line?.adjust_type_value],
      adjust_name: [event_line?.adjust_name],
      used: [event_line?.used ?? false],
      used_settlement_line_ids: [event_line?.used_settlement_line_ids ?? []],
      amount: [event_line?.amount ?? 0],
    });
    this.event_form_array.push(group);
  }
}
